# Discovered Components

This is an auto-generated list of components discovered by [nuxt/components](https://github.com/nuxt/components).

You can directly use them in pages and other components without the need to import them.

**Tip:** If a component is conditionally rendered with `v-if` and is big, it is better to use `Lazy` or `lazy-` prefix to lazy load.

- `<LawyerIndexComponent>` | `<lawyer-index-component>` (components/lawyer/indexComponent.vue)
- `<LawyerCommonChartComponent>` | `<lawyer-common-chart-component>` (components/lawyer/common/ChartComponent.vue)
- `<LawyerCommonDivTextSearch>` | `<lawyer-common-div-text-search>` (components/lawyer/common/DivTextSearch.vue)
- `<LawyerCommonDocumentAIChat>` | `<lawyer-common-document-a-i-chat>` (components/lawyer/common/DocumentAIChat.vue)
- `<LawyerCommonFileUploadModal>` | `<lawyer-common-file-upload-modal>` (components/lawyer/common/FileUploadModal.vue)
- `<LawyerCrawlConfigIndexComponent>` | `<lawyer-crawl-config-index-component>` (components/lawyer/crawlConfig/indexComponent.vue)
- `<LawyerCrawlStatisticsIndexComponent>` | `<lawyer-crawl-statistics-index-component>` (components/lawyer/crawlStatistics/indexComponent.vue)
- `<LawyerIndexDashboardOverview>` | `<lawyer-index-dashboard-overview>` (components/lawyer/index/DashboardOverview.vue)
- `<LawyerIndexLatestUpdates>` | `<lawyer-index-latest-updates>` (components/lawyer/index/LatestUpdates.vue)
- `<LawyerIndexReviewTable>` | `<lawyer-index-review-table>` (components/lawyer/index/ReviewTable.vue)
- `<LawyerIndexSourceDistribution>` | `<lawyer-index-source-distribution>` (components/lawyer/index/SourceDistribution.vue)
- `<LawyerDocumentCompare>` | `<lawyer-document-compare>` (components/lawyer/document/DocumentCompare.vue)
- `<LawyerDocumentViewer>` | `<lawyer-document-viewer>` (components/lawyer/document/DocumentViewer.vue)
- `<LawyerManualReviewDetailComponent>` | `<lawyer-manual-review-detail-component>` (components/lawyer/manualReview/detailComponent.vue)
- `<LawyerManualReviewIndexComponent>` | `<lawyer-manual-review-index-component>` (components/lawyer/manualReview/indexComponent.vue)
- `<LawyerKnowledgeDetailComponent>` | `<lawyer-knowledge-detail-component>` (components/lawyer/knowledge/detailComponent.vue)
- `<LawyerKnowledgeIndexComponent>` | `<lawyer-knowledge-index-component>` (components/lawyer/knowledge/indexComponent.vue)
- `<LawyerUpdatesDetailComponent>` | `<lawyer-updates-detail-component>` (components/lawyer/updates/detailComponent.vue)
- `<LawyerUpdatesIndexComponent>` | `<lawyer-updates-index-component>` (components/lawyer/updates/indexComponent.vue)
